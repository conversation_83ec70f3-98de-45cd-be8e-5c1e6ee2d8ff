package org.dromara.sol.websocket.manager;

import org.dromara.sol.websocket.config.WebSocketConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WebSocket速率限制管理器测试
 */
@ExtendWith(MockitoExtension.class)
class WebSocketRateLimitManagerTest {

    @Mock
    private WebSocketConfig webSocketConfig;
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private WebSocketLogManager logManager;
    
    @Mock
    private RRateLimiter secondLimiter;
    
    @Mock
    private RRateLimiter minuteLimiter;
    
    @Mock
    private RRateLimiter hourLimiter;

    private WebSocketRateLimitManager rateLimitManager;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(webSocketConfig.getMaxRequestsPerSecond()).thenReturn(15);
        when(webSocketConfig.getMaxRequestsPerMinute()).thenReturn(300);
        when(webSocketConfig.getMaxRequestsPerHour()).thenReturn(10000);
        when(webSocketConfig.getRateLimitBuffer()).thenReturn(0.8);
        when(webSocketConfig.isEnableRequestQueue()).thenReturn(true);
        when(webSocketConfig.getMaxQueueSize()).thenReturn(1000);
        
        // 模拟Redis限流器
        when(redissonClient.getRateLimiter(anyString())).thenReturn(secondLimiter, minuteLimiter, hourLimiter);
        when(secondLimiter.trySetRate(any(), anyInt(), any())).thenReturn(true);
        when(minuteLimiter.trySetRate(any(), anyInt(), any())).thenReturn(true);
        when(hourLimiter.trySetRate(any(), anyInt(), any())).thenReturn(true);
        
        rateLimitManager = new WebSocketRateLimitManager(webSocketConfig, redissonClient, logManager);
    }

    @Test
    void testInitialization() {
        // 测试初始化
        assertDoesNotThrow(() -> {
            rateLimitManager.init();
        });
        
        // 验证限流器设置被调用
        verify(secondLimiter).trySetRate(any(), eq(12), any()); // 15 * 0.8 = 12
        verify(minuteLimiter).trySetRate(any(), eq(240), any()); // 300 * 0.8 = 240
        verify(hourLimiter).trySetRate(any(), eq(8000), any()); // 10000 * 0.8 = 8000
    }

    @Test
    void testTryAcquireSuccess() {
        rateLimitManager.init();
        
        // 模拟所有限流器都允许请求
        when(secondLimiter.tryAcquire(1)).thenReturn(true);
        when(minuteLimiter.tryAcquire(1)).thenReturn(true);
        when(hourLimiter.tryAcquire(1)).thenReturn(true);
        
        boolean result = rateLimitManager.tryAcquire("test operation");
        
        assertTrue(result);
        verify(secondLimiter).tryAcquire(1);
        verify(minuteLimiter).tryAcquire(1);
        verify(hourLimiter).tryAcquire(1);
    }

    @Test
    void testTryAcquireFailure() {
        rateLimitManager.init();
        
        // 模拟秒级限流器拒绝请求
        when(secondLimiter.tryAcquire(1)).thenReturn(false);
        
        boolean result = rateLimitManager.tryAcquire("test operation");
        
        assertFalse(result);
        verify(secondLimiter).tryAcquire(1);
        // 其他限流器不应该被调用
        verify(minuteLimiter, never()).tryAcquire(1);
        verify(hourLimiter, never()).tryAcquire(1);
    }

    @Test
    void testTryAcquireAsyncSuccess() {
        rateLimitManager.init();
        
        // 模拟所有限流器都允许请求
        when(secondLimiter.tryAcquire(1)).thenReturn(true);
        when(minuteLimiter.tryAcquire(1)).thenReturn(true);
        when(hourLimiter.tryAcquire(1)).thenReturn(true);
        
        boolean[] taskExecuted = {false};
        Runnable task = () -> taskExecuted[0] = true;
        
        boolean result = rateLimitManager.tryAcquireAsync("test operation", task);
        
        assertTrue(result);
        assertTrue(taskExecuted[0]);
    }

    @Test
    void testTryAcquireAsyncWithQueue() {
        rateLimitManager.init();
        
        // 模拟限流器拒绝请求，但队列可用
        when(secondLimiter.tryAcquire(1)).thenReturn(false);
        
        boolean[] taskExecuted = {false};
        Runnable task = () -> taskExecuted[0] = true;
        
        boolean result = rateLimitManager.tryAcquireAsync("test operation", task);
        
        assertTrue(result); // 应该成功加入队列
        assertFalse(taskExecuted[0]); // 任务还未执行
    }

    @Test
    void testTryAcquireAsyncQueueDisabled() {
        // 禁用队列
        when(webSocketConfig.isEnableRequestQueue()).thenReturn(false);
        
        WebSocketRateLimitManager noQueueManager = new WebSocketRateLimitManager(webSocketConfig, redissonClient, logManager);
        noQueueManager.init();
        
        // 模拟限流器拒绝请求
        when(secondLimiter.tryAcquire(1)).thenReturn(false);
        
        boolean[] taskExecuted = {false};
        Runnable task = () -> taskExecuted[0] = true;
        
        boolean result = noQueueManager.tryAcquireAsync("test operation", task);
        
        assertFalse(result); // 队列禁用时应该直接失败
        assertFalse(taskExecuted[0]);
    }

    @Test
    void testGetStatus() {
        rateLimitManager.init();
        
        // 模拟一些请求
        when(secondLimiter.tryAcquire(1)).thenReturn(true);
        when(minuteLimiter.tryAcquire(1)).thenReturn(true);
        when(hourLimiter.tryAcquire(1)).thenReturn(true);
        when(secondLimiter.availablePermits()).thenReturn(10L);
        when(minuteLimiter.availablePermits()).thenReturn(200L);
        when(hourLimiter.availablePermits()).thenReturn(5000L);
        
        rateLimitManager.tryAcquire("test");
        
        WebSocketRateLimitManager.RateLimitStatus status = rateLimitManager.getStatus();
        
        assertNotNull(status);
        assertTrue(status.getTotalRequests() > 0);
        assertEquals(10L, status.getSecondLimiterAvailable());
        assertEquals(200L, status.getMinuteLimiterAvailable());
        assertEquals(5000L, status.getHourLimiterAvailable());
    }

    @Test
    void testReportStats() {
        rateLimitManager.init();
        
        // 测试统计报告
        assertDoesNotThrow(() -> {
            rateLimitManager.reportStats();
        });
    }

    @Test
    void testDestroy() {
        rateLimitManager.init();
        
        // 测试销毁
        assertDoesNotThrow(() -> {
            rateLimitManager.destroy();
        });
    }

    @Test
    void testExceptionHandling() {
        // 模拟Redis异常
        when(redissonClient.getRateLimiter(anyString())).thenThrow(new RuntimeException("Redis error"));
        
        // 初始化应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            rateLimitManager.init();
        });
    }

    @Test
    void testTaskExecutionException() {
        rateLimitManager.init();
        
        // 模拟所有限流器都允许请求
        when(secondLimiter.tryAcquire(1)).thenReturn(true);
        when(minuteLimiter.tryAcquire(1)).thenReturn(true);
        when(hourLimiter.tryAcquire(1)).thenReturn(true);
        
        // 创建会抛异常的任务
        Runnable failingTask = () -> {
            throw new RuntimeException("Task execution failed");
        };
        
        // 应该能处理任务执行异常
        boolean result = rateLimitManager.tryAcquireAsync("test operation", failingTask);
        
        assertTrue(result); // 提交成功
        // 异常应该被捕获和记录，不会向上传播
    }

    @Test
    void testRateLimitBuffer() {
        // 测试不同的缓冲比例
        when(webSocketConfig.getRateLimitBuffer()).thenReturn(0.5);
        
        WebSocketRateLimitManager bufferManager = new WebSocketRateLimitManager(webSocketConfig, redissonClient, logManager);
        bufferManager.init();
        
        // 验证缓冲比例被正确应用
        verify(secondLimiter).trySetRate(any(), eq(7), any()); // 15 * 0.5 = 7.5 -> 7
        verify(minuteLimiter).trySetRate(any(), eq(150), any()); // 300 * 0.5 = 150
        verify(hourLimiter).trySetRate(any(), eq(5000), any()); // 10000 * 0.5 = 5000
    }

    @Test
    void testMultipleRequests() {
        rateLimitManager.init();
        
        // 模拟前几个请求成功，后面的失败
        when(secondLimiter.tryAcquire(1))
            .thenReturn(true)
            .thenReturn(true)
            .thenReturn(false);
        when(minuteLimiter.tryAcquire(1)).thenReturn(true);
        when(hourLimiter.tryAcquire(1)).thenReturn(true);
        
        // 前两个请求应该成功
        assertTrue(rateLimitManager.tryAcquire("request 1"));
        assertTrue(rateLimitManager.tryAcquire("request 2"));
        
        // 第三个请求应该失败
        assertFalse(rateLimitManager.tryAcquire("request 3"));
        
        WebSocketRateLimitManager.RateLimitStatus status = rateLimitManager.getStatus();
        assertEquals(3, status.getTotalRequests());
        assertEquals(1, status.getRejectedRequests());
    }
}
