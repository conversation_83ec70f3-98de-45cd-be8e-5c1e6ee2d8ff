package org.dromara.sol.websocket;

import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.core.WebSocketConnection;
import org.dromara.sol.websocket.impl.SolanaWebSocketManagerImpl;
import org.dromara.sol.websocket.manager.WebSocketLogManager;
import org.dromara.sol.websocket.manager.WebSocketRateLimitManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 测试InterruptedException的正确处理
 */
@ExtendWith(MockitoExtension.class)
class InterruptedExceptionHandlingTest {

    @Mock
    private WebSocketConfig webSocketConfig;
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private WebSocketLogManager logManager;
    
    @Mock
    private RRateLimiter rateLimiter;

    @Test
    void testWebSocketConnectionInterruptHandling() throws Exception {
        // 设置配置
        when(webSocketConfig.getConnectionTimeout()).thenReturn(1);
        
        // 创建WebSocket连接
        URI uri = new URI("ws://localhost:8080");
        WebSocketConnection connection = new WebSocketConnection(uri, webSocketConfig, logManager);
        
        // 在另一个线程中中断当前线程
        Thread currentThread = Thread.currentThread();
        Thread interruptThread = new Thread(() -> {
            try {
                Thread.sleep(100); // 等待连接开始
                currentThread.interrupt();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        interruptThread.start();
        
        // 测试连接方法是否正确处理中断
        boolean result = connection.connect();
        
        // 验证结果
        assertFalse(result);
        assertTrue(Thread.interrupted()); // 清除中断状态
        
        interruptThread.join();
    }

    @Test
    void testWebSocketManagerWaitForConnectionInterruptHandling() throws Exception {
        // 创建模拟的WebSocketManager
        SolanaWebSocketManagerImpl manager = mock(SolanaWebSocketManagerImpl.class);
        
        // 模拟waitForConnection方法抛出InterruptedException
        when(manager.waitForConnection(anyLong(), any(TimeUnit.class)))
            .thenThrow(new InterruptedException("Test interrupt"));
        
        // 测试中断处理
        assertThrows(InterruptedException.class, () -> {
            manager.waitForConnection(1, TimeUnit.SECONDS);
        });
    }

    @Test
    void testRateLimitManagerInterruptHandling() throws Exception {
        // 设置配置
        when(webSocketConfig.getMaxRequestsPerSecond()).thenReturn(10);
        when(webSocketConfig.getMaxRequestsPerMinute()).thenReturn(100);
        when(webSocketConfig.getMaxRequestsPerHour()).thenReturn(1000);
        when(webSocketConfig.getRateLimitBuffer()).thenReturn(0.8);
        when(webSocketConfig.isEnableRequestQueue()).thenReturn(true);
        when(webSocketConfig.getMaxQueueSize()).thenReturn(100);
        
        // 模拟Redis限流器
        when(redissonClient.getRateLimiter(anyString())).thenReturn(rateLimiter);
        when(rateLimiter.trySetRate(any(), anyInt(), any())).thenReturn(true);
        
        // 模拟限流器在等待时被中断
        when(rateLimiter.tryAcquire(eq(1), anyLong(), any(TimeUnit.class)))
            .thenThrow(new InterruptedException("Test interrupt"));
        
        WebSocketRateLimitManager rateLimitManager = new WebSocketRateLimitManager(
            webSocketConfig, redissonClient, logManager);
        rateLimitManager.init();
        
        // 测试异步获取时的中断处理
        boolean[] taskExecuted = {false};
        Runnable task = () -> taskExecuted[0] = true;
        
        // 这应该能正确处理中断而不会导致程序崩溃
        boolean result = rateLimitManager.tryAcquireAsync("test", task);
        
        // 任务可能被加入队列或直接失败，但不应该抛出未处理的异常
        assertFalse(taskExecuted[0]); // 由于中断，任务不应该执行
    }

    @Test
    void testCountDownLatchInterruptHandling() throws Exception {
        CountDownLatch latch = new CountDownLatch(1);
        
        // 在另一个线程中中断当前线程
        Thread currentThread = Thread.currentThread();
        Thread interruptThread = new Thread(() -> {
            try {
                Thread.sleep(100);
                currentThread.interrupt();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        interruptThread.start();
        
        // 测试CountDownLatch.await的中断处理
        boolean result = false;
        boolean interrupted = false;
        
        try {
            result = latch.await(1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            interrupted = true;
            Thread.currentThread().interrupt(); // 恢复中断状态
        }
        
        // 验证中断被正确处理
        assertTrue(interrupted);
        assertFalse(result);
        assertTrue(Thread.interrupted()); // 清除中断状态
        
        interruptThread.join();
    }

    @Test
    void testThreadSleepInterruptHandling() {
        // 在另一个线程中中断当前线程
        Thread currentThread = Thread.currentThread();
        Thread interruptThread = new Thread(() -> {
            try {
                Thread.sleep(100);
                currentThread.interrupt();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        interruptThread.start();
        
        // 测试Thread.sleep的中断处理
        boolean interrupted = false;
        
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            interrupted = true;
            Thread.currentThread().interrupt(); // 恢复中断状态
        }
        
        // 验证中断被正确处理
        assertTrue(interrupted);
        assertTrue(Thread.interrupted()); // 清除中断状态
        
        try {
            interruptThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testInterruptStatusPreservation() {
        // 测试中断状态的保持
        Thread.currentThread().interrupt();
        
        // 检查中断状态
        assertTrue(Thread.currentThread().isInterrupted());
        
        // 模拟正确的中断处理
        if (Thread.currentThread().isInterrupted()) {
            // 在实际代码中，这里应该进行清理并重新设置中断状态
            Thread.interrupted(); // 清除中断状态
            
            // 执行清理操作...
            
            // 重新设置中断状态
            Thread.currentThread().interrupt();
        }
        
        // 验证中断状态被正确保持
        assertTrue(Thread.currentThread().isInterrupted());
        
        // 清除中断状态以免影响其他测试
        Thread.interrupted();
    }

    @Test
    void testMultipleInterruptHandling() throws Exception {
        // 测试多个可能被中断的操作
        CountDownLatch latch1 = new CountDownLatch(1);
        CountDownLatch latch2 = new CountDownLatch(1);
        
        Thread currentThread = Thread.currentThread();
        Thread interruptThread = new Thread(() -> {
            try {
                Thread.sleep(50);
                currentThread.interrupt();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        interruptThread.start();
        
        boolean firstInterrupted = false;
        boolean secondInterrupted = false;
        
        // 第一个可能被中断的操作
        try {
            latch1.await(1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            firstInterrupted = true;
            Thread.currentThread().interrupt(); // 恢复中断状态
        }
        
        // 第二个操作应该立即检测到中断状态
        try {
            latch2.await(1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            secondInterrupted = true;
            Thread.currentThread().interrupt(); // 恢复中断状态
        }
        
        // 验证两个操作都正确处理了中断
        assertTrue(firstInterrupted);
        assertTrue(secondInterrupted);
        assertTrue(Thread.interrupted()); // 清除中断状态
        
        interruptThread.join();
    }
}
