package org.dromara.sol.websocket.manager;

import org.dromara.sol.websocket.config.WebSocketConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocket日志管理器测试
 */
@ExtendWith(MockitoExtension.class)
class WebSocketLogManagerTest {

    @Mock
    private WebSocketConfig webSocketConfig;

    private WebSocketLogManager logManager;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(webSocketConfig.isVerboseLogging()).thenReturn(false);
        when(webSocketConfig.getMessageLogSampleRate()).thenReturn(100);
        when(webSocketConfig.getLogAggregationWindow()).thenReturn(60);
        when(webSocketConfig.isSubscriptionStatsEnabled()).thenReturn(true);
        
        logManager = new WebSocketLogManager(webSocketConfig);
    }

    @Test
    void testLogConnection() {
        // 测试连接成功日志
        assertDoesNotThrow(() -> {
            logManager.logConnection("wss://test.com", true);
        });

        // 测试连接失败日志
        assertDoesNotThrow(() -> {
            logManager.logConnection("wss://test.com", false);
        });
    }

    @Test
    void testLogMessageReceived() {
        String testMessage = "{\"jsonrpc\":\"2.0\",\"method\":\"test\"}";
        
        // 测试消息接收日志
        assertDoesNotThrow(() -> {
            logManager.logMessageReceived(testMessage);
        });

        // 测试空消息
        assertDoesNotThrow(() -> {
            logManager.logMessageReceived("");
        });

        // 测试null消息
        assertDoesNotThrow(() -> {
            logManager.logMessageReceived(null);
        });
    }

    @Test
    void testLogMessageSent() {
        String testMessage = "{\"jsonrpc\":\"2.0\",\"method\":\"test\"}";
        
        // 测试消息发送成功
        assertDoesNotThrow(() -> {
            logManager.logMessageSent(testMessage, true);
        });

        // 测试消息发送失败
        assertDoesNotThrow(() -> {
            logManager.logMessageSent(testMessage, false);
        });
    }

    @Test
    void testLogSubscription() {
        String address = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM";
        String coinType = "SOL";
        
        // 测试订阅成功
        assertDoesNotThrow(() -> {
            logManager.logSubscription(address, coinType, true);
        });

        // 测试订阅失败
        assertDoesNotThrow(() -> {
            logManager.logSubscription(address, coinType, false);
        });
    }

    @Test
    void testLogError() {
        String operation = "TestOperation";
        String error = "Test error message";
        Exception exception = new RuntimeException("Test exception");
        
        // 测试带异常的错误日志
        assertDoesNotThrow(() -> {
            logManager.logError(operation, error, exception);
        });

        // 测试不带异常的错误日志
        assertDoesNotThrow(() -> {
            logManager.logError(operation, error, null);
        });
    }

    @Test
    void testGetStatsSummary() {
        // 记录一些统计数据
        logManager.logConnection("wss://test.com", true);
        logManager.logMessageReceived("test message");
        logManager.logMessageSent("test message", true);
        logManager.logSubscription("test_address", "SOL", true);
        
        String summary = logManager.getStatsSummary();
        assertNotNull(summary);
        assertTrue(summary.contains("WebSocket统计"));
        assertTrue(summary.contains("连接:"));
        assertTrue(summary.contains("接收:"));
        assertTrue(summary.contains("发送:"));
        assertTrue(summary.contains("订阅:"));
    }

    @Test
    void testReportDetailedStats() {
        // 启用统计报告
        when(webSocketConfig.isSubscriptionStatsEnabled()).thenReturn(true);
        
        // 测试详细统计报告
        assertDoesNotThrow(() -> {
            logManager.reportDetailedStats();
        });

        // 禁用统计报告
        when(webSocketConfig.isSubscriptionStatsEnabled()).thenReturn(false);
        
        // 测试禁用状态下的统计报告
        assertDoesNotThrow(() -> {
            logManager.reportDetailedStats();
        });
    }

    @Test
    void testVerboseLoggingMode() {
        // 启用详细日志模式
        when(webSocketConfig.isVerboseLogging()).thenReturn(true);
        
        WebSocketLogManager verboseLogManager = new WebSocketLogManager(webSocketConfig);
        
        // 测试详细模式下的日志记录
        assertDoesNotThrow(() -> {
            verboseLogManager.logConnection("wss://test.com", true);
            verboseLogManager.logMessageReceived("test message");
            verboseLogManager.logSubscription("test_address", "SOL", true);
        });
    }

    @Test
    void testMessageSampling() {
        // 设置低采样率
        when(webSocketConfig.getMessageLogSampleRate()).thenReturn(1);
        
        WebSocketLogManager samplingLogManager = new WebSocketLogManager(webSocketConfig);
        
        // 测试采样模式下的消息记录
        for (int i = 0; i < 100; i++) {
            assertDoesNotThrow(() -> {
                samplingLogManager.logMessageReceived("test message " + i);
            });
        }
    }

    @Test
    void testZeroSampleRate() {
        // 设置0采样率
        when(webSocketConfig.getMessageLogSampleRate()).thenReturn(0);
        
        WebSocketLogManager noSamplingLogManager = new WebSocketLogManager(webSocketConfig);
        
        // 测试0采样率下的消息记录
        assertDoesNotThrow(() -> {
            noSamplingLogManager.logMessageReceived("test message");
        });
    }

    @Test
    void testFullSampleRate() {
        // 设置100%采样率
        when(webSocketConfig.getMessageLogSampleRate()).thenReturn(100);
        
        WebSocketLogManager fullSamplingLogManager = new WebSocketLogManager(webSocketConfig);
        
        // 测试100%采样率下的消息记录
        assertDoesNotThrow(() -> {
            fullSamplingLogManager.logMessageReceived("test message");
        });
    }

    @Test
    void testLongMessage() {
        // 创建长消息
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 200; i++) {
            longMessage.append("This is a test message. ");
        }
        
        // 测试长消息的处理
        assertDoesNotThrow(() -> {
            logManager.logMessageReceived(longMessage.toString());
            logManager.logMessageSent(longMessage.toString(), true);
        });
    }

    @Test
    void testAddressMasking() {
        String longAddress = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM";
        String shortAddress = "123";
        
        // 测试长地址和短地址的掩码处理
        assertDoesNotThrow(() -> {
            logManager.logSubscription(longAddress, "SOL", true);
            logManager.logSubscription(shortAddress, "SOL", true);
            logManager.logSubscription(null, "SOL", true);
        });
    }
}
