package org.dromara.sol.demo;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.startup.SolanaStartupInitializer;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.springframework.boot.ApplicationArguments;

/**
 * 启动优化演示程序
 * 展示优化后的启动流程和日志输出
 */
@Slf4j
public class StartupOptimizationDemo {

    public static void main(String[] args) {
        StartupOptimizationDemo demo = new StartupOptimizationDemo();
        demo.demonstrateOptimizedStartup();
    }

    public void demonstrateOptimizedStartup() {
        log.info("=== 启动优化演示 ===");
        
        // 模拟Spring Boot应用启动完成
        simulateApplicationStartup();
        
        // 演示优化后的启动流程
        demonstrateStartupFlow();
        
        // 演示日志优化效果
        demonstrateLogOptimization();
        
        log.info("=== 演示完成 ===");
    }

    /**
     * 模拟应用启动
     */
    private void simulateApplicationStartup() {
        log.info("🚀 模拟Spring Boot应用启动...");
        
        try {
            // 模拟应用启动时间
            Thread.sleep(2000);
            
            log.info("✅ Spring Boot应用启动完成");
            log.info("🎯 现在开始按顺序初始化Solana组件...");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 演示启动流程
     */
    private void demonstrateStartupFlow() {
        log.info("\n--- 优化后的启动流程 ---");
        
        // 第一步：WebSocket连接
        log.info("📡 第一步：WebSocket连接初始化");
        log.info("  - 延迟启动：避免与应用启动冲突");
        log.info("  - 异步连接：不阻塞后续组件初始化");
        log.info("  - 简化日志：减少重复的连接日志");
        
        simulateDelay(2000);
        log.info("✅ WebSocket连接初始化完成");
        
        // 第二步：地址监控
        log.info("\n👁️ 第二步：地址监控初始化");
        log.info("  - 等待WebSocket连接建立");
        log.info("  - 异步订阅地址");
        log.info("  - 清晰的进度日志");
        
        simulateDelay(3000);
        log.info("✅ 地址监控初始化完成");
        
        // 第三步：遗漏交易检查
        log.info("\n🔍 第三步：遗漏交易检查");
        log.info("  - 延迟5分钟启动");
        log.info("  - 避免与地址监控日志混乱");
        log.info("  - 独立的日志标识");
        
        log.info("⏰ 遗漏交易检查将在5分钟后启动");
    }

    /**
     * 演示日志优化效果
     */
    private void demonstrateLogOptimization() {
        log.info("\n--- 日志优化效果 ---");
        
        log.info("🎯 优化前的问题:");
        log.info("  ❌ 重复的'websocket连接'日志");
        log.info("  ❌ 启动时立即连接，日志混乱");
        log.info("  ❌ 地址监控和遗漏交易检查同时执行");
        
        log.info("\n✅ 优化后的改进:");
        log.info("  ✅ 使用emoji和统一格式的日志");
        log.info("  ✅ 分阶段启动，日志清晰有序");
        log.info("  ✅ 时间分离，避免日志混乱");
        log.info("  ✅ 配置化的延迟时间");
        
        log.info("\n📊 日志级别优化:");
        log.info("  - DEBUG: 详细的连接过程");
        log.info("  - INFO: 重要的状态变化");
        log.info("  - WARN: 需要注意的问题");
        log.info("  - ERROR: 需要处理的错误");
    }

    /**
     * 模拟延迟
     */
    private void simulateDelay(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
