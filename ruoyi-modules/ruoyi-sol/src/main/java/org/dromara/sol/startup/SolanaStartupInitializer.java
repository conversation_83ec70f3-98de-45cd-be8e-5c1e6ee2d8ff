package org.dromara.sol.startup;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.SolanaAccountWatcherFacade;
import org.dromara.sol.websocket.SolanaTransactionScanner;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Solana系统启动初始化器
 * 在Spring Boot应用完全启动后，按顺序初始化各个组件
 * 
 * 执行顺序：
 * 1. WebSocket连接建立
 * 2. 地址监控和订阅
 * 3. 遗漏交易补偿检查
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(100) // 确保在其他ApplicationRunner之后执行
public class SolanaStartupInitializer implements ApplicationRunner {

    private final SolanaWebSocketManager webSocketManager;
    private final SolanaAccountWatcherFacade accountWatcherFacade;
    private final SolanaTransactionScanner transactionScanner;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🚀 开始初始化Solana系统组件...");
        
        try {
            // 第一步：建立WebSocket连接
            initializeWebSocketConnection();
            
            // 第二步：等待一段时间，然后初始化地址监控
            initializeAddressMonitoring();
            
            // 第三步：启动遗漏交易补偿检查（延迟启动）
            initializeTransactionScanner();

            log.info("✅ Solana系统组件初始化完成");
            log.info("📋 启动摘要:");
            log.info("  - WebSocket连接: 已启动");
            log.info("  - 地址监控: 已启动");
            log.info("  - 遗漏交易检查: 将在5分钟后启动");
            
        } catch (Exception e) {
            log.error("❌ Solana系统组件初始化失败", e);
            throw e;
        }
    }

    /**
     * 初始化WebSocket连接
     */
    private void initializeWebSocketConnection() {
        log.info("📡 第一步：初始化WebSocket连接...");
        
        try {
            // 异步初始化WebSocket连接
            CompletableFuture.runAsync(() -> {
                try {
                    webSocketManager.initializeWebSocketClient();
                } catch (Exception e) {
                    log.error("WebSocket连接初始化失败", e);
                }
            });
            
            log.info("✅ WebSocket连接初始化已启动");
            
        } catch (Exception e) {
            log.error("❌ WebSocket连接初始化失败", e);
            throw new RuntimeException("WebSocket连接初始化失败", e);
        }
    }

    /**
     * 初始化地址监控
     */
    private void initializeAddressMonitoring() {
        log.info("👁️ 第二步：初始化地址监控...");
        
        try {
            // 等待WebSocket连接建立
            boolean connected = waitForWebSocketConnection();
            if (!connected) {
                log.warn("⚠️ WebSocket连接未建立，但继续初始化地址监控");
            }
            
            // 异步初始化地址监控，避免阻塞启动
            CompletableFuture.runAsync(() -> {
                try {
                    // 这里会调用原来的run方法逻辑
                    accountWatcherFacade.run(null);
                } catch (Exception e) {
                    log.error("地址监控初始化失败", e);
                }
            });
            
            log.info("✅ 地址监控初始化已启动");
            
        } catch (Exception e) {
            log.error("❌ 地址监控初始化失败", e);
            throw new RuntimeException("地址监控初始化失败", e);
        }
    }

    /**
     * 初始化遗漏交易扫描器
     */
    private void initializeTransactionScanner() {
        log.info("🔍 第三步：初始化遗漏交易扫描器...");
        
        try {
            // 延迟启动遗漏交易检查，避免与地址监控日志混乱
            CompletableFuture.runAsync(() -> {
                try {
                    // 等待5分钟后再启动遗漏交易检查
                    log.info("⏰ 遗漏交易检查将在5分钟后启动，避免与地址监控日志混乱");
                    Thread.sleep(5 * 60 * 1000); // 5分钟
                    
                    log.info("🔍 开始启动遗漏交易检查定时任务...");
                    transactionScanner.startMissedTransactionChecker();
                    log.info("✅ 遗漏交易检查定时任务已启动");
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("遗漏交易检查启动被中断");
                } catch (Exception e) {
                    log.error("遗漏交易检查启动失败", e);
                }
            });
            
            log.info("✅ 遗漏交易扫描器初始化已安排");
            
        } catch (Exception e) {
            log.error("❌ 遗漏交易扫描器初始化失败", e);
            // 这个不是关键组件，失败了不影响系统启动
            log.warn("遗漏交易扫描器初始化失败，但系统将继续运行");
        }
    }

    /**
     * 等待WebSocket连接建立
     */
    private boolean waitForWebSocketConnection() {
        log.info("⏳ 等待WebSocket连接建立...");
        
        try {
            // 最多等待30秒
            for (int i = 0; i < 30; i++) {
                if (webSocketManager.isConnected()) {
                    log.info("✅ WebSocket连接已建立");
                    return true;
                }
                Thread.sleep(1000);
                
                if (i % 5 == 0) {
                    log.debug("等待WebSocket连接... ({}/30秒)", i + 1);
                }
            }
            
            log.warn("⚠️ WebSocket连接等待超时");
            return false;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("WebSocket连接等待被中断");
            return false;
        }
    }
}
