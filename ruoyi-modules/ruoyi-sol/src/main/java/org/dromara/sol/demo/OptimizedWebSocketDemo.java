package org.dromara.sol.demo;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.core.WebSocketConnection;
import org.dromara.sol.websocket.manager.WebSocketLogManager;
import org.dromara.sol.websocket.strategy.ReconnectStrategy;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 优化后的WebSocket演示程序
 * 展示简化后的WebSocket实现的使用方法
 */
@Slf4j
public class OptimizedWebSocketDemo {

    private static final String QUICKNODE_WSS_URL = "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/";

    public static void main(String[] args) {
        OptimizedWebSocketDemo demo = new OptimizedWebSocketDemo();
        demo.demonstrateOptimizedWebSocket();
    }

    public void demonstrateOptimizedWebSocket() {
        log.info("=== 优化后的WebSocket演示 ===");

        // 1. 创建配置
        WebSocketConfig config = createOptimizedConfig();
        log.info("✅ 配置创建完成");

        // 2. 创建重连策略
        ReconnectStrategy reconnectStrategy = new ReconnectStrategy(config);
        log.info("✅ 重连策略创建完成");

        // 3. 创建WebSocket连接
        WebSocketConnection connection = null;
        try {
            connection = new WebSocketConnection(new URI(QUICKNODE_WSS_URL), config);
            log.info("✅ WebSocket连接对象创建完成");

            // 4. 设置简单的回调
            setupSimpleCallbacks(connection);
            log.info("✅ 回调设置完成");

            // 5. 演示连接
            demonstrateConnection(connection);

            // 6. 演示重连策略
            demonstrateReconnectStrategy(reconnectStrategy);

            // 7. 演示消息发送
            demonstrateMessageSending(connection);

        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        } finally {
            // 8. 清理资源
            cleanup(connection, reconnectStrategy);
        }

        log.info("=== 演示完成 ===");
    }

    /**
     * 创建优化的配置
     */
    private WebSocketConfig createOptimizedConfig() {
        WebSocketConfig config = new WebSocketConfig();
        
        // 简化的配置 - 只设置必要的参数
        config.setConnectionTimeout(10);        // 10秒连接超时
        config.setMaxReconnectAttempts(3);      // 最多重连3次
        config.setInitialReconnectDelay(1000);  // 初始延迟1秒
        config.setMaxReconnectDelay(5000);      // 最大延迟5秒
        config.setAutoReconnect(true);          // 启用自动重连
        config.setHeartbeatEnabled(false);      // 演示时禁用心跳
        
        log.info("配置详情:");
        log.info("  - 连接超时: {} 秒", config.getConnectionTimeout());
        log.info("  - 最大重连次数: {}", config.getMaxReconnectAttempts());
        log.info("  - 自动重连: {}", config.isAutoReconnect());
        
        return config;
    }

    /**
     * 设置简单的回调函数
     */
    private void setupSimpleCallbacks(WebSocketConnection connection) {
        connection.setOnOpenCallback(handshake -> {
            log.info("🎉 连接成功! HTTP状态: {}", handshake.getHttpStatus());
        });

        connection.setOnMessageCallback(message -> {
            log.info("📨 收到消息: {}", message.length() > 100 ? 
                message.substring(0, 100) + "..." : message);
        });

        connection.setOnCloseCallback(closeInfo -> {
            log.info("🔌 连接关闭: [{}] {}", closeInfo.getCode(), closeInfo.getReason());
        });

        connection.setOnErrorCallback(error -> {
            log.error("❌ 连接错误: {}", error.getMessage());
        });
    }

    /**
     * 演示连接功能
     */
    private void demonstrateConnection(WebSocketConnection connection) {
        log.info("\n--- 演示连接功能 ---");
        
        try {
            // 异步连接
            log.info("🔄 开始异步连接...");
            connection.connectAsync().thenAccept(success -> {
                if (success) {
                    log.info("✅ 异步连接成功!");
                } else {
                    log.warn("⚠️ 异步连接失败");
                }
            });

            // 等待连接结果
            Thread.sleep(3000);

            // 检查连接状态
            log.info("连接状态: {}", connection.isConnected() ? "已连接" : "未连接");

        } catch (Exception e) {
            log.error("连接演示失败", e);
        }
    }

    /**
     * 演示重连策略
     */
    private void demonstrateReconnectStrategy(ReconnectStrategy reconnectStrategy) {
        log.info("\n--- 演示重连策略 ---");
        
        CountDownLatch latch = new CountDownLatch(2);
        AtomicBoolean taskExecuted = new AtomicBoolean(false);
        
        Runnable testTask = () -> {
            log.info("🔄 重连任务执行 (尝试次数: {})", reconnectStrategy.getCurrentAttemptCount());
            taskExecuted.set(true);
            latch.countDown();
        };

        // 安排两次重连
        log.info("安排第一次重连...");
        reconnectStrategy.scheduleReconnect(testTask);
        
        log.info("安排第二次重连...");
        reconnectStrategy.scheduleReconnect(testTask);

        try {
            // 等待重连任务执行
            boolean completed = latch.await(10, TimeUnit.SECONDS);
            if (completed) {
                log.info("✅ 重连策略演示成功");
            } else {
                log.warn("⚠️ 重连策略演示超时");
            }
            
            // 重置计数器演示
            log.info("重置重连计数器...");
            reconnectStrategy.resetAttemptCount();
            log.info("当前重连次数: {}", reconnectStrategy.getCurrentAttemptCount());
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("重连策略演示被中断", e);
        }
    }

    /**
     * 演示消息发送
     */
    private void demonstrateMessageSending(WebSocketConnection connection) {
        log.info("\n--- 演示消息发送 ---");
        
        if (!connection.isConnected()) {
            log.warn("⚠️ 连接未建立，跳过消息发送演示");
            return;
        }

        // 发送测试消息
        String testMessage = """
            {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getHealth"
            }
            """;

        log.info("📤 发送测试消息...");
        boolean sent = connection.sendMessage(testMessage);
        
        if (sent) {
            log.info("✅ 消息发送成功");
            
            // 等待响应
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        } else {
            log.warn("⚠️ 消息发送失败");
        }
    }

    /**
     * 清理资源
     */
    private void cleanup(WebSocketConnection connection, ReconnectStrategy reconnectStrategy) {
        log.info("\n--- 清理资源 ---");
        
        try {
            if (connection != null) {
                connection.close();
                log.info("✅ WebSocket连接已关闭");
            }
            
            if (reconnectStrategy != null) {
                reconnectStrategy.shutdown();
                log.info("✅ 重连策略已关闭");
            }
            
        } catch (Exception e) {
            log.error("清理资源时发生错误", e);
        }
    }
}
