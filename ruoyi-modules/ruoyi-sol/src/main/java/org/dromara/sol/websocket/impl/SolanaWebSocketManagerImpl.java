package org.dromara.sol.websocket.impl;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.event.WebSocketMessageEvent;
import org.dromara.sol.exception.SolanaURIFormatException;
import org.dromara.sol.exception.SolanaWebSocketConnectionException;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.core.WebSocketConnection;
import org.dromara.sol.websocket.strategy.ReconnectStrategy;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Solana WebSocket连接管理器实现 - 简化版本
 * 使用组合模式，将复杂的逻辑分离到专门的类中
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaWebSocketManagerImpl implements SolanaWebSocketManager {

    private final SolRpcConfig rpcConfig;
    private final ApplicationEventPublisher eventPublisher;
    private final WebSocketConfig webSocketConfig;
    private final ReconnectStrategy reconnectStrategy;

    private WebSocketConnection connection;
    private ScheduledExecutorService heartbeatExecutor;

    // 回调函数
    private Consumer<ServerHandshake> onOpenCallback;
    private Consumer<String> onMessageCallback;
    private Consumer<CloseInfo> onCloseCallback;

    /**
     * 初始化方法 - 只初始化组件，不立即连接
     */
    @PostConstruct
    public void init() {
        log.info("初始化SolanaWebSocketManager组件");

        if (webSocketConfig.isHeartbeatEnabled()) {
            startHeartbeat();
        }
    }

    /**
     * 启动心跳检查
     */
    private void startHeartbeat() {
        heartbeatExecutor = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r, "websocket-heartbeat");
            thread.setDaemon(true);
            return thread;
        });

        heartbeatExecutor.scheduleAtFixedRate(() -> {
            try {
                if (!isConnected()) {
                    log.debug("心跳检查发现连接断开，尝试重连");
                    reconnectStrategy.scheduleReconnect(this::reconnect);
                }
            } catch (Exception e) {
                log.error("心跳检查异常", e);
            }
        }, webSocketConfig.getHeartbeatInterval(), webSocketConfig.getHeartbeatInterval(), TimeUnit.SECONDS);
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void cleanup() {
        try {
            if (connection != null) {
                connection.close();
            }

            if (heartbeatExecutor != null) {
                heartbeatExecutor.shutdown();
            }

            reconnectStrategy.shutdown();

            log.info("SolanaWebSocketManager清理完成");
        } catch (Exception e) {
            log.error("SolanaWebSocketManager清理过程中发生错误", e);
        }
    }

    @Override
    public void initializeWebSocketClient() {
        try {
            String websocketUrl = rpcConfig.getWebsocketUrl();
            if (websocketUrl == null || websocketUrl.trim().isEmpty()) {
                log.error("WebSocket URL未配置，无法建立连接");
                return;
            }

            URI uri = new URI(websocketUrl);
            connection = new WebSocketConnection(uri, webSocketConfig);

            // 设置回调
            setupCallbacks();

            // 异步连接
            connection.connectAsync().thenAccept(success -> {
                if (success) {
                    log.info("WebSocket连接成功建立");
                    reconnectStrategy.resetAttemptCount();
                } else {
                    log.error("WebSocket连接失败，安排重连");
                    reconnectStrategy.scheduleReconnect(this::reconnect);
                }
            });

        } catch (Exception e) {
            log.error("初始化WebSocket失败", e);
            reconnectStrategy.scheduleReconnect(this::reconnect);
        }
    }

    @Override
    public boolean isConnected() {
        return connection != null && connection.isConnected();
    }

    @Override
    public boolean sendMessage(String message) {
        if (connection == null) {
            log.warn("WebSocket连接未初始化");
            return false;
        }
        return connection.sendMessage(message);
    }

    @Override
    public void closeConnection() {
        if (connection != null) {
            connection.close();
        }
    }

    /**
     * 重连方法
     */
    private void reconnect() {
        if (connection != null) {
            boolean success = connection.reconnect();
            if (success) {
                reconnectStrategy.resetAttemptCount();
            }
        } else {
            initializeWebSocketClient();
        }
    }

    @Override
    public void setOnOpenCallback(Consumer<ServerHandshake> onOpenCallback) {
        this.onOpenCallback = onOpenCallback;
        if (connection != null) {
            connection.setOnOpenCallback(onOpenCallback);
        }
    }

    @Override
    public void setOnMessageCallback(Consumer<String> onMessageCallback) {
        this.onMessageCallback = onMessageCallback;
        if (connection != null) {
            connection.setOnMessageCallback(onMessageCallback);
        }
    }

    @Override
    public void setOnCloseCallback(Consumer<CloseInfo> onCloseCallback) {
        this.onCloseCallback = onCloseCallback;
        if (connection != null) {
            connection.setOnCloseCallback(closeInfo -> {
                CloseInfo info = new CloseInfo(closeInfo.getCode(), closeInfo.getReason(), closeInfo.isRemote());
                onCloseCallback.accept(info);
            });
        }
    }

    @Override
    public boolean waitForConnection(long timeout, TimeUnit unit) throws InterruptedException {
        if (connection == null) {
            return false;
        }

        // 简单的轮询等待
        long endTime = System.currentTimeMillis() + unit.toMillis(timeout);
        try {
            while (System.currentTimeMillis() < endTime) {
                if (connection.isConnected()) {
                    return true;
                }
                Thread.sleep(100);
            }
            return false;
        } catch (InterruptedException e) {
            // 恢复中断状态并重新抛出
            Thread.currentThread().interrupt();
            throw e;
        }
    }

    /**
     * 设置连接回调
     */
    private void setupCallbacks() {
        if (connection == null) return;

        connection.setOnOpenCallback(handshake -> {
            log.info("WebSocket连接已建立");
            if (onOpenCallback != null) {
                onOpenCallback.accept(handshake);
            }
        });

        connection.setOnMessageCallback(message -> {
            // 发布事件
            eventPublisher.publishEvent(new WebSocketMessageEvent(this, message));

            if (onMessageCallback != null) {
                onMessageCallback.accept(message);
            }
        });

        connection.setOnCloseCallback(closeInfo -> {
            log.info("WebSocket连接关闭: [{}] {}", closeInfo.getCode(), closeInfo.getReason());

            if (onCloseCallback != null) {
                CloseInfo info = new CloseInfo(closeInfo.getCode(), closeInfo.getReason(), closeInfo.isRemote());
                onCloseCallback.accept(info);
            }

            // 安排重连
            if (webSocketConfig.isAutoReconnect()) {
                reconnectStrategy.scheduleReconnect(this::reconnect);
            }
        });

        connection.setOnErrorCallback(error -> {
            log.error("WebSocket发生错误", error);
        });
    }
}
