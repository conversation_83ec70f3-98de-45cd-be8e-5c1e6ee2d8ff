package org.dromara.sol.websocket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * WebSocket配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sol.websocket")
public class WebSocketConfig {

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 5;

    /**
     * 初始重连延迟（毫秒）
     */
    private long initialReconnectDelay = 1000;

    /**
     * 最大重连延迟（毫秒）
     */
    private long maxReconnectDelay = 30000;

    /**
     * 重连冷却期（毫秒）
     */
    private long reconnectCooldownPeriod = 300000;

    /**
     * 心跳检查间隔（秒）
     */
    private int heartbeatInterval = 60;

    /**
     * 是否启用自动重连
     */
    private boolean autoReconnect = true;

    /**
     * 是否启用心跳检查
     */
    private boolean heartbeatEnabled = true;

    // ========== 日志优化配置 ==========

    /**
     * 是否启用详细日志（开发环境使用）
     */
    private boolean verboseLogging = false;

    /**
     * 消息日志采样率（1-100，表示百分比）
     */
    private int messageLogSampleRate = 1;

    /**
     * 日志聚合时间窗口（秒）
     */
    private int logAggregationWindow = 60;

    /**
     * 是否启用订阅统计日志
     */
    private boolean subscriptionStatsEnabled = true;

    // ========== 速率限制配置 ==========

    /**
     * 每秒最大请求数（QuickNode限制）
     */
    private int maxRequestsPerSecond = 15;

    /**
     * 每分钟最大请求数
     */
    private int maxRequestsPerMinute = 300;

    /**
     * 每小时最大请求数
     */
    private int maxRequestsPerHour = 10000;

    /**
     * 速率限制缓冲比例（0.8表示使用80%的限制）
     */
    private double rateLimitBuffer = 0.8;

    /**
     * 是否启用请求队列（当达到限制时排队等待）
     */
    private boolean enableRequestQueue = true;

    /**
     * 请求队列最大长度
     */
    private int maxQueueSize = 1000;
}
