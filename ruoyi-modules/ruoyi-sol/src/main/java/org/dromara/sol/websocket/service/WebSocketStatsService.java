package org.dromara.sol.websocket.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.manager.WebSocketLogManager;
import org.dromara.sol.websocket.manager.WebSocketRateLimitManager;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * WebSocket统计服务
 * 定期输出WebSocket相关的统计信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketStatsService {

    private final WebSocketConfig webSocketConfig;
    private final WebSocketLogManager logManager;
    private final WebSocketRateLimitManager rateLimitManager;

    /**
     * 每5分钟输出一次统计信息
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void reportStats() {
        if (!webSocketConfig.isSubscriptionStatsEnabled()) {
            return;
        }

        try {
            log.info("=== WebSocket统计报告 ===");
            
            // 输出基础统计
            log.info(logManager.getStatsSummary());
            
            // 输出详细统计
            logManager.reportDetailedStats();
            
            // 输出速率限制统计
            rateLimitManager.reportStats();
            
            log.info("=== 统计报告结束 ===");
            
        } catch (Exception e) {
            log.error("输出统计报告失败", e);
        }
    }

    /**
     * 每小时输出一次速率限制状态
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void reportRateLimitStatus() {
        try {
            WebSocketRateLimitManager.RateLimitStatus status = rateLimitManager.getStatus();
            
            log.info("📊 每小时速率限制报告:");
            log.info("  - 总请求数: {}", status.getTotalRequests());
            log.info("  - 拒绝请求数: {} (拒绝率: {:.2f}%)", 
                status.getRejectedRequests(),
                status.getTotalRequests() > 0 ? 
                    (double) status.getRejectedRequests() / status.getTotalRequests() * 100 : 0);
            log.info("  - 当前队列长度: {}", status.getQueueSize());
            
            // 如果拒绝率过高，输出警告
            if (status.getTotalRequests() > 100) {
                double rejectRate = (double) status.getRejectedRequests() / status.getTotalRequests() * 100;
                if (rejectRate > 10) {
                    log.warn("⚠️ 请求拒绝率过高 ({:.2f}%)，建议检查速率限制配置", rejectRate);
                }
            }
            
        } catch (Exception e) {
            log.error("输出速率限制状态失败", e);
        }
    }

    /**
     * 每天凌晨重置统计计数器（可选）
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨
    public void dailyReset() {
        try {
            log.info("🔄 执行每日统计重置");
            
            // 输出最终统计
            log.info("📊 昨日最终统计:");
            log.info(logManager.getStatsSummary());
            rateLimitManager.reportStats();
            
            // 这里可以添加重置逻辑，如果需要的话
            // 注意：当前实现使用累计计数，不进行重置
            
        } catch (Exception e) {
            log.error("每日重置失败", e);
        }
    }
}
