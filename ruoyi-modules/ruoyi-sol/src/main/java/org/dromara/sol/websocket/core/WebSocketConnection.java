package org.dromara.sol.websocket.core;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.manager.WebSocketLogManager;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * WebSocket连接封装类
 * 负责单个WebSocket连接的管理
 */
@Slf4j
public class WebSocketConnection {
    
    private final URI uri;
    private final WebSocketConfig config;
    private final WebSocketLogManager logManager;
    private final AtomicBoolean connected = new AtomicBoolean(false);

    private WebSocketClient client;
    private CountDownLatch connectionLatch;
    
    // 事件回调
    private Consumer<ServerHandshake> onOpenCallback;
    private Consumer<String> onMessageCallback;
    private Consumer<ConnectionCloseInfo> onCloseCallback;
    private Consumer<Exception> onErrorCallback;
    
    public WebSocketConnection(URI uri, WebSocketConfig config, WebSocketLogManager logManager) {
        this.uri = uri;
        this.config = config;
        this.logManager = logManager;
        this.connectionLatch = new CountDownLatch(1);
    }
    
    /**
     * 异步连接到WebSocket服务器
     * 
     * @return CompletableFuture，完成时表示连接结果
     */
    public CompletableFuture<Boolean> connectAsync() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return connect();
            } catch (Exception e) {
                log.error("异步连接失败", e);
                return false;
            }
        });
    }
    
    /**
     * 同步连接到WebSocket服务器
     * 
     * @return 连接是否成功
     */
    public boolean connect() {
        try {
            log.debug("正在连接到WebSocket: {}", uri);

            // 重置连接状态
            connected.set(false);
            connectionLatch = new CountDownLatch(1);

            // 创建WebSocket客户端
            client = createWebSocketClient();
            client.setConnectionLostTimeout(config.getConnectionTimeout());

            // 启动连接
            client.connect();

            // 等待连接建立
            boolean success = connectionLatch.await(config.getConnectionTimeout(), TimeUnit.SECONDS);

            if (success && connected.get()) {
                logManager.logConnection(uri.toString(), true);
                return true;
            } else {
                logManager.logConnection(uri.toString(), false);
                return false;
            }

        } catch (Exception e) {
            logManager.logError("Connection", "WebSocket连接异常", e);
            return false;
        }
    }
    
    /**
     * 发送消息
     */
    public boolean sendMessage(String message) {
        if (!isConnected()) {
            log.warn("WebSocket未连接，无法发送消息");
            return false;
        }
        
        try {
            client.send(message);
            logManager.logMessageSent(message, true);
            return true;
        } catch (Exception e) {
            logManager.logMessageSent(message, false);
            logManager.logError("SendMessage", "发送WebSocket消息失败", e);
            return false;
        }
    }
    
    /**
     * 关闭连接
     */
    public void close() {
        if (client != null && client.isOpen()) {
            try {
                client.close();
                log.info("WebSocket连接已关闭");
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败", e);
            }
        }
        connected.set(false);
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return connected.get() && client != null && client.isOpen();
    }
    
    /**
     * 重连
     */
    public boolean reconnect() {
        log.debug("尝试重新连接WebSocket");
        close();
        return connect();
    }
    
    /**
     * 创建WebSocket客户端
     */
    private WebSocketClient createWebSocketClient() {
        return new WebSocketClient(uri) {
            @Override
            public void onOpen(ServerHandshake handshake) {
                log.debug("WebSocket连接已打开，状态: {}", handshake.getHttpStatus());
                connected.set(true);
                connectionLatch.countDown();

                if (onOpenCallback != null) {
                    try {
                        onOpenCallback.accept(handshake);
                    } catch (Exception e) {
                        log.error("执行onOpen回调失败", e);
                    }
                }
            }
            
            @Override
            public void onMessage(String message) {
                log.debug("收到WebSocket消息: {} 字节", message.length());
                
                if (onMessageCallback != null) {
                    try {
                        onMessageCallback.accept(message);
                    } catch (Exception e) {
                        log.error("执行onMessage回调失败", e);
                    }
                }
            }
            
            @Override
            public void onClose(int code, String reason, boolean remote) {
                log.info("WebSocket连接关闭: [{}] {} (远程: {})", code, reason, remote);
                connected.set(false);
                
                if (onCloseCallback != null) {
                    try {
                        onCloseCallback.accept(new ConnectionCloseInfo(code, reason, remote));
                    } catch (Exception e) {
                        log.error("执行onClose回调失败", e);
                    }
                }
            }
            
            @Override
            public void onError(Exception ex) {
                log.error("WebSocket发生错误", ex);
                connectionLatch.countDown();
                
                if (onErrorCallback != null) {
                    try {
                        onErrorCallback.accept(ex);
                    } catch (Exception e) {
                        log.error("执行onError回调失败", e);
                    }
                }
            }
        };
    }
    
    // Setter方法用于设置回调
    public void setOnOpenCallback(Consumer<ServerHandshake> callback) {
        this.onOpenCallback = callback;
    }
    
    public void setOnMessageCallback(Consumer<String> callback) {
        this.onMessageCallback = callback;
    }
    
    public void setOnCloseCallback(Consumer<ConnectionCloseInfo> callback) {
        this.onCloseCallback = callback;
    }
    
    public void setOnErrorCallback(Consumer<Exception> callback) {
        this.onErrorCallback = callback;
    }
    
    /**
     * 连接关闭信息
     */
    public static class ConnectionCloseInfo {
        private final int code;
        private final String reason;
        private final boolean remote;
        
        public ConnectionCloseInfo(int code, String reason, boolean remote) {
            this.code = code;
            this.reason = reason;
            this.remote = remote;
        }
        
        public int getCode() { return code; }
        public String getReason() { return reason; }
        public boolean isRemote() { return remote; }
    }
}
