package org.dromara.sol.websocket.manager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * WebSocket日志管理器
 * 负责优化WebSocket相关的日志记录，减少日志量并提供统计信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketLogManager {

    private final WebSocketConfig webSocketConfig;
    
    // 统计计数器
    private final LongAdder totalMessagesReceived = new LongAdder();
    private final LongAdder totalMessagesSent = new LongAdder();
    private final LongAdder totalSubscriptions = new LongAdder();
    private final LongAdder totalConnections = new LongAdder();
    
    // 时间窗口统计
    private final ConcurrentHashMap<String, WindowStats> windowStats = new ConcurrentHashMap<>();
    
    // 上次统计报告时间
    private volatile long lastStatsReport = System.currentTimeMillis();
    
    /**
     * 记录连接事件
     */
    public void logConnection(String endpoint, boolean success) {
        totalConnections.increment();
        
        if (webSocketConfig.isVerboseLogging()) {
            if (success) {
                log.info("✅ WebSocket连接成功: {}", endpoint);
            } else {
                log.warn("❌ WebSocket连接失败: {}", endpoint);
            }
        } else {
            // 简化日志，只记录关键信息
            if (success) {
                log.info("✅ WebSocket连接成功");
            } else {
                log.warn("❌ WebSocket连接失败");
            }
        }
        
        // 定期输出统计信息
        checkAndReportStats();
    }
    
    /**
     * 记录消息接收事件（采样记录）
     */
    public void logMessageReceived(String message) {
        totalMessagesReceived.increment();
        
        // 采样记录详细消息
        if (shouldLogMessage()) {
            if (webSocketConfig.isVerboseLogging()) {
                log.debug("📨 收到消息: {}", truncateMessage(message));
            } else {
                log.debug("📨 收到消息 ({}字节)", message.length());
            }
        }
        
        // 更新时间窗口统计
        updateWindowStats("messages_received");
    }
    
    /**
     * 记录消息发送事件
     */
    public void logMessageSent(String message, boolean success) {
        if (success) {
            totalMessagesSent.increment();
        }
        
        if (webSocketConfig.isVerboseLogging()) {
            if (success) {
                log.debug("📤 消息发送成功: {}", truncateMessage(message));
            } else {
                log.warn("📤 消息发送失败: {}", truncateMessage(message));
            }
        } else {
            if (success) {
                log.debug("📤 消息发送成功 ({}字节)", message.length());
            } else {
                log.warn("📤 消息发送失败");
            }
        }
        
        updateWindowStats("messages_sent");
    }
    
    /**
     * 记录订阅事件
     */
    public void logSubscription(String address, String coinType, boolean success) {
        if (success) {
            totalSubscriptions.increment();
        }
        
        if (webSocketConfig.isVerboseLogging()) {
            if (success) {
                log.info("🔔 地址订阅成功: {} ({})", address, coinType);
            } else {
                log.warn("🔔 地址订阅失败: {} ({})", address, coinType);
            }
        } else {
            // 聚合日志，减少单个地址的日志输出
            if (success) {
                log.debug("🔔 地址订阅成功: {} ({})", maskAddress(address), coinType);
            } else {
                log.warn("🔔 地址订阅失败: {} ({})", maskAddress(address), coinType);
            }
        }
        
        updateWindowStats("subscriptions");
    }
    
    /**
     * 记录错误事件
     */
    public void logError(String operation, String error, Throwable throwable) {
        // 错误日志始终记录
        if (throwable != null) {
            log.error("❌ {}失败: {}", operation, error, throwable);
        } else {
            log.error("❌ {}失败: {}", operation, error);
        }
    }
    
    /**
     * 获取统计信息摘要
     */
    public String getStatsSummary() {
        return String.format(
            "WebSocket统计 - 连接:%d, 接收:%d, 发送:%d, 订阅:%d",
            totalConnections.sum(),
            totalMessagesReceived.sum(),
            totalMessagesSent.sum(),
            totalSubscriptions.sum()
        );
    }
    
    /**
     * 输出详细统计报告
     */
    public void reportDetailedStats() {
        if (!webSocketConfig.isSubscriptionStatsEnabled()) {
            return;
        }
        
        long now = System.currentTimeMillis();
        long timeSinceLastReport = now - lastStatsReport;
        
        if (timeSinceLastReport >= webSocketConfig.getLogAggregationWindow() * 1000L) {
            log.info("📊 WebSocket统计报告 [{}]:", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            log.info("  - 总连接数: {}", totalConnections.sum());
            log.info("  - 总接收消息: {}", totalMessagesReceived.sum());
            log.info("  - 总发送消息: {}", totalMessagesSent.sum());
            log.info("  - 总订阅数: {}", totalSubscriptions.sum());
            
            // 输出时间窗口统计
            windowStats.forEach((key, stats) -> {
                long rate = stats.count.sum() * 60000 / timeSinceLastReport; // 每分钟速率
                log.info("  - {}: {} ({}次/分钟)", key, stats.count.sum(), rate);
            });
            
            // 重置时间窗口统计
            windowStats.clear();
            lastStatsReport = now;
        }
    }
    
    /**
     * 判断是否应该记录消息（采样）
     */
    private boolean shouldLogMessage() {
        int sampleRate = webSocketConfig.getMessageLogSampleRate();
        if (sampleRate >= 100) {
            return true;
        }
        if (sampleRate <= 0) {
            return false;
        }
        return ThreadLocalRandom.current().nextInt(100) < sampleRate;
    }
    
    /**
     * 截断消息内容
     */
    private String truncateMessage(String message) {
        if (message == null) {
            return "null";
        }
        if (message.length() <= 100) {
            return message;
        }
        return message.substring(0, 100) + "...";
    }
    
    /**
     * 掩码地址（隐私保护）
     */
    private String maskAddress(String address) {
        if (address == null || address.length() < 8) {
            return address;
        }
        return address.substring(0, 4) + "****" + address.substring(address.length() - 4);
    }
    
    /**
     * 更新时间窗口统计
     */
    private void updateWindowStats(String key) {
        windowStats.computeIfAbsent(key, k -> new WindowStats()).count.increment();
    }
    
    /**
     * 检查并报告统计信息
     */
    private void checkAndReportStats() {
        reportDetailedStats();
    }
    
    /**
     * 时间窗口统计数据
     */
    private static class WindowStats {
        private final LongAdder count = new LongAdder();
    }
}
