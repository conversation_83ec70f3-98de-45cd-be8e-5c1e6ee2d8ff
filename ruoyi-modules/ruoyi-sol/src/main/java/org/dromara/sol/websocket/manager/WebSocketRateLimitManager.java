package org.dromara.sol.websocket.manager;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebSocket速率限制管理器
 * 提供多层级的速率限制，防止超过QuickNode的API限制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketRateLimitManager {

    private final WebSocketConfig webSocketConfig;
    private final RedissonClient redissonClient;
    private final WebSocketLogManager logManager;
    
    // 多级限流器
    private RRateLimiter secondLimiter;
    private RRateLimiter minuteLimiter;
    private RRateLimiter hourLimiter;
    
    // 请求队列
    private BlockingQueue<Runnable> requestQueue;
    private ScheduledExecutorService queueProcessor;
    
    // 统计计数器
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong rejectedRequests = new AtomicLong(0);
    private final AtomicLong queuedRequests = new AtomicLong(0);
    
    @PostConstruct
    public void init() {
        initRateLimiters();
        if (webSocketConfig.isEnableRequestQueue()) {
            initRequestQueue();
        }
        log.info("WebSocket速率限制管理器初始化完成");
        logRateLimitConfig();
    }
    
    @PreDestroy
    public void destroy() {
        if (queueProcessor != null && !queueProcessor.isShutdown()) {
            queueProcessor.shutdown();
            try {
                if (!queueProcessor.awaitTermination(5, TimeUnit.SECONDS)) {
                    queueProcessor.shutdownNow();
                }
            } catch (InterruptedException e) {
                queueProcessor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 尝试获取执行许可（同步）
     * 
     * @param operation 操作描述
     * @return 是否获得许可
     */
    public boolean tryAcquire(String operation) {
        totalRequests.incrementAndGet();
        
        try {
            // 检查多级限流器
            if (!checkAllLimiters()) {
                rejectedRequests.incrementAndGet();
                logManager.logError("RateLimit", 
                    String.format("请求被限流拒绝: %s (总请求:%d, 拒绝:%d)", 
                        operation, totalRequests.get(), rejectedRequests.get()), null);
                return false;
            }
            
            log.debug("✅ 速率限制检查通过: {}", operation);
            return true;
            
        } catch (Exception e) {
            logManager.logError("RateLimit", "速率限制检查异常: " + operation, e);
            return false;
        }
    }
    
    /**
     * 尝试获取执行许可（异步，支持排队）
     * 
     * @param operation 操作描述
     * @param task 要执行的任务
     * @return 是否成功提交（立即执行或加入队列）
     */
    public boolean tryAcquireAsync(String operation, Runnable task) {
        totalRequests.incrementAndGet();
        
        // 先尝试立即执行
        if (tryAcquire(operation)) {
            try {
                task.run();
                return true;
            } catch (Exception e) {
                logManager.logError("TaskExecution", "任务执行异常: " + operation, e);
                return false;
            }
        }
        
        // 如果启用了队列，则加入队列等待
        if (webSocketConfig.isEnableRequestQueue() && requestQueue != null) {
            if (requestQueue.size() < webSocketConfig.getMaxQueueSize()) {
                requestQueue.offer(() -> {
                    try {
                        // 等待获取许可后执行
                        waitAndAcquire(operation);
                        task.run();
                        queuedRequests.decrementAndGet();
                    } catch (Exception e) {
                        logManager.logError("QueuedTaskExecution", "队列任务执行异常: " + operation, e);
                        queuedRequests.decrementAndGet();
                    }
                });
                queuedRequests.incrementAndGet();
                log.debug("📋 请求已加入队列: {} (队列长度: {})", operation, requestQueue.size());
                return true;
            } else {
                rejectedRequests.incrementAndGet();
                logManager.logError("QueueFull", "请求队列已满，拒绝请求: " + operation, null);
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * 获取当前速率限制状态
     */
    public RateLimitStatus getStatus() {
        return RateLimitStatus.builder()
            .totalRequests(totalRequests.get())
            .rejectedRequests(rejectedRequests.get())
            .queuedRequests(queuedRequests.get())
            .queueSize(requestQueue != null ? requestQueue.size() : 0)
            .secondLimiterAvailable(secondLimiter != null ? secondLimiter.availablePermits() : 0)
            .minuteLimiterAvailable(minuteLimiter != null ? minuteLimiter.availablePermits() : 0)
            .hourLimiterAvailable(hourLimiter != null ? hourLimiter.availablePermits() : 0)
            .build();
    }
    
    /**
     * 输出速率限制统计信息
     */
    public void reportStats() {
        RateLimitStatus status = getStatus();
        log.info("📊 速率限制统计:");
        log.info("  - 总请求数: {}", status.getTotalRequests());
        log.info("  - 拒绝请求数: {}", status.getRejectedRequests());
        log.info("  - 队列中请求: {}", status.getQueuedRequests());
        log.info("  - 可用许可 - 秒级:{}, 分钟级:{}, 小时级:{}", 
            status.getSecondLimiterAvailable(),
            status.getMinuteLimiterAvailable(), 
            status.getHourLimiterAvailable());
    }
    
    /**
     * 初始化限流器
     */
    private void initRateLimiters() {
        try {
            // 计算实际限制（应用缓冲比例）
            int actualSecondLimit = (int) (webSocketConfig.getMaxRequestsPerSecond() * webSocketConfig.getRateLimitBuffer());
            int actualMinuteLimit = (int) (webSocketConfig.getMaxRequestsPerMinute() * webSocketConfig.getRateLimitBuffer());
            int actualHourLimit = (int) (webSocketConfig.getMaxRequestsPerHour() * webSocketConfig.getRateLimitBuffer());
            
            // 秒级限流器
            secondLimiter = redissonClient.getRateLimiter("solana:websocket:rate_limiter:second");
            secondLimiter.trySetRate(RateType.OVERALL, actualSecondLimit, Duration.of(1, ChronoUnit.SECONDS));
            
            // 分钟级限流器
            minuteLimiter = redissonClient.getRateLimiter("solana:websocket:rate_limiter:minute");
            minuteLimiter.trySetRate(RateType.OVERALL, actualMinuteLimit, Duration.of(1, ChronoUnit.MINUTES));
            
            // 小时级限流器
            hourLimiter = redissonClient.getRateLimiter("solana:websocket:rate_limiter:hour");
            hourLimiter.trySetRate(RateType.OVERALL, actualHourLimit, Duration.of(1, ChronoUnit.HOURS));
            
        } catch (Exception e) {
            log.error("初始化限流器失败", e);
            throw new RuntimeException("限流器初始化失败", e);
        }
    }
    
    /**
     * 初始化请求队列
     */
    private void initRequestQueue() {
        requestQueue = new LinkedBlockingQueue<>(webSocketConfig.getMaxQueueSize());
        
        // 创建队列处理器
        queueProcessor = new ScheduledThreadPoolExecutor(2, r -> {
            Thread thread = new Thread(r, "websocket-rate-limit-queue");
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动队列处理任务
        queueProcessor.scheduleAtFixedRate(this::processQueue, 100, 100, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 检查所有限流器
     */
    private boolean checkAllLimiters() {
        return secondLimiter.tryAcquire(1) && 
               minuteLimiter.tryAcquire(1) && 
               hourLimiter.tryAcquire(1);
    }
    
    /**
     * 等待并获取许可
     */
    private void waitAndAcquire(String operation) {
        try {
            // 最多等待5秒
            boolean acquired = secondLimiter.tryAcquire(1, 5, TimeUnit.SECONDS) &&
                              minuteLimiter.tryAcquire(1, 5, TimeUnit.SECONDS) &&
                              hourLimiter.tryAcquire(1, 5, TimeUnit.SECONDS);
            
            if (!acquired) {
                throw new RuntimeException("等待速率限制许可超时: " + operation);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("等待速率限制许可被中断: " + operation, e);
        }
    }
    
    /**
     * 处理队列中的请求
     */
    private void processQueue() {
        try {
            Runnable task = requestQueue.poll();
            if (task != null) {
                task.run();
            }
        } catch (Exception e) {
            log.error("处理队列任务异常", e);
        }
    }
    
    /**
     * 记录速率限制配置
     */
    private void logRateLimitConfig() {
        log.info("速率限制配置:");
        log.info("  - 每秒最大请求: {} (缓冲后: {})", 
            webSocketConfig.getMaxRequestsPerSecond(),
            (int)(webSocketConfig.getMaxRequestsPerSecond() * webSocketConfig.getRateLimitBuffer()));
        log.info("  - 每分钟最大请求: {} (缓冲后: {})", 
            webSocketConfig.getMaxRequestsPerMinute(),
            (int)(webSocketConfig.getMaxRequestsPerMinute() * webSocketConfig.getRateLimitBuffer()));
        log.info("  - 每小时最大请求: {} (缓冲后: {})", 
            webSocketConfig.getMaxRequestsPerHour(),
            (int)(webSocketConfig.getMaxRequestsPerHour() * webSocketConfig.getRateLimitBuffer()));
        log.info("  - 请求队列: {} (最大长度: {})", 
            webSocketConfig.isEnableRequestQueue() ? "启用" : "禁用",
            webSocketConfig.getMaxQueueSize());
    }
    
    /**
     * 速率限制状态
     */
    @lombok.Data
    @lombok.Builder
    public static class RateLimitStatus {
        private long totalRequests;
        private long rejectedRequests;
        private long queuedRequests;
        private int queueSize;
        private long secondLimiterAvailable;
        private long minuteLimiterAvailable;
        private long hourLimiterAvailable;
    }
}
